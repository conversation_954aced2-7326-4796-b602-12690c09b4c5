# API Configuration
REACT_APP_API_URL=http://localhost:5000/api
REACT_APP_SOCKET_URL=http://localhost:5000

# App Configuration
REACT_APP_NAME=Unified Social Platform
REACT_APP_VERSION=1.0.0

# Feature Flags
REACT_APP_ENABLE_NOTIFICATIONS=true
REACT_APP_ENABLE_VIDEO_CALLS=true
REACT_APP_ENABLE_LIVE_STREAMING=true
REACT_APP_ENABLE_MARKETPLACE=true

# Media Configuration
REACT_APP_MAX_FILE_SIZE=10485760
REACT_APP_SUPPORTED_IMAGE_FORMATS=jpg,jpeg,png,gif,webp
REACT_APP_SUPPORTED_VIDEO_FORMATS=mp4,webm,mov,avi

# Social Media Integration
REACT_APP_FACEBOOK_APP_ID=your-facebook-app-id
REACT_APP_GOOGLE_CLIENT_ID=your-google-client-id

# Analytics
REACT_APP_GOOGLE_ANALYTICS_ID=your-ga-id
REACT_APP_ENABLE_ANALYTICS=false

# Development
REACT_APP_DEBUG_MODE=true
REACT_APP_LOG_LEVEL=debug
