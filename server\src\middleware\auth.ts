import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

interface AuthRequest extends Request {
  user?: {
    id: string;
    email: string;
    username: string;
  };
}

interface JwtPayload {
  userId: string;
  email: string;
  username: string;
}

export const authenticateToken = async (
  req: AuthRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    if (!token) {
      res.status(401).json({ 
        error: 'Access token required',
        message: 'Please provide a valid access token'
      });
      return;
    }

    const jwtSecret = process.env.JWT_SECRET;
    if (!jwtSecret) {
      res.status(500).json({ 
        error: 'Server configuration error',
        message: 'JWT secret not configured'
      });
      return;
    }

    // Verify the token
    const decoded = jwt.verify(token, jwtSecret) as JwtPayload;

    // Check if user still exists and is active
    const user = await prisma.user.findUnique({
      where: { 
        id: decoded.userId,
        isActive: true
      },
      select: {
        id: true,
        email: true,
        username: true,
        isActive: true,
        emailVerified: true
      }
    });

    if (!user) {
      res.status(401).json({ 
        error: 'Invalid token',
        message: 'User not found or account deactivated'
      });
      return;
    }

    if (!user.emailVerified) {
      res.status(401).json({ 
        error: 'Email not verified',
        message: 'Please verify your email address'
      });
      return;
    }

    // Attach user info to request
    req.user = {
      id: user.id,
      email: user.email,
      username: user.username
    };

    next();
  } catch (error) {
    if (error instanceof jwt.JsonWebTokenError) {
      res.status(401).json({ 
        error: 'Invalid token',
        message: 'The provided token is invalid or expired'
      });
      return;
    }

    console.error('Authentication error:', error);
    res.status(500).json({ 
      error: 'Authentication failed',
      message: 'An error occurred during authentication'
    });
  }
};

export const optionalAuth = async (
  req: AuthRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1];

    if (!token) {
      // No token provided, continue without authentication
      next();
      return;
    }

    const jwtSecret = process.env.JWT_SECRET;
    if (!jwtSecret) {
      next();
      return;
    }

    const decoded = jwt.verify(token, jwtSecret) as JwtPayload;
    const user = await prisma.user.findUnique({
      where: { 
        id: decoded.userId,
        isActive: true
      },
      select: {
        id: true,
        email: true,
        username: true
      }
    });

    if (user) {
      req.user = {
        id: user.id,
        email: user.email,
        username: user.username
      };
    }

    next();
  } catch (error) {
    // If token is invalid, continue without authentication
    next();
  }
};

export const requireEmailVerification = (
  req: AuthRequest,
  res: Response,
  next: NextFunction
): void => {
  // This middleware should be used after authenticateToken
  if (!req.user) {
    res.status(401).json({ 
      error: 'Authentication required',
      message: 'Please log in to access this resource'
    });
    return;
  }

  // The email verification check is already done in authenticateToken
  next();
};

export const generateToken = (user: { id: string; email: string; username: string }): string => {
  const jwtSecret = process.env.JWT_SECRET;
  if (!jwtSecret) {
    throw new Error('JWT secret not configured');
  }

  const expiresIn = process.env.JWT_EXPIRES_IN || '7d';

  return jwt.sign(
    {
      userId: user.id,
      email: user.email,
      username: user.username
    },
    jwtSecret,
    { expiresIn }
  );
};

export const generateRefreshToken = (userId: string): string => {
  const refreshSecret = process.env.REFRESH_TOKEN_SECRET;
  if (!refreshSecret) {
    throw new Error('Refresh token secret not configured');
  }

  return jwt.sign(
    { userId },
    refreshSecret,
    { expiresIn: '30d' }
  );
};

export const verifyRefreshToken = (token: string): { userId: string } => {
  const refreshSecret = process.env.REFRESH_TOKEN_SECRET;
  if (!refreshSecret) {
    throw new Error('Refresh token secret not configured');
  }

  return jwt.verify(token, refreshSecret) as { userId: string };
};

export { AuthRequest };
