#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Setting up Unified Social Media Platform...\n');

// Function to run commands
function runCommand(command, description) {
  console.log(`📦 ${description}...`);
  try {
    execSync(command, { stdio: 'inherit' });
    console.log(`✅ ${description} completed!\n`);
  } catch (error) {
    console.error(`❌ Error during ${description}:`, error.message);
    process.exit(1);
  }
}

// Function to copy environment files
function copyEnvFiles() {
  console.log('📝 Setting up environment files...');
  
  const envFiles = [
    { src: 'server/.env.example', dest: 'server/.env' },
    { src: 'client/.env.example', dest: 'client/.env' }
  ];

  envFiles.forEach(({ src, dest }) => {
    if (fs.existsSync(src) && !fs.existsSync(dest)) {
      fs.copyFileSync(src, dest);
      console.log(`✅ Created ${dest}`);
    }
  });
  console.log('');
}

// Main setup process
async function setup() {
  try {
    // Install root dependencies
    runCommand('npm install', 'Installing root dependencies');

    // Install server dependencies
    runCommand('cd server && npm install', 'Installing server dependencies');

    // Install client dependencies
    runCommand('cd client && npm install', 'Installing client dependencies');

    // Copy environment files
    copyEnvFiles();

    console.log('🎉 Setup completed successfully!\n');
    console.log('🌐 To start your website:');
    console.log('   npm run dev\n');
    console.log('📱 Then open: http://localhost:3000');
    console.log('🔧 API will be at: http://localhost:5000\n');
    console.log('💡 Demo login: Use any email and password!');

  } catch (error) {
    console.error('❌ Setup failed:', error.message);
    process.exit(1);
  }
}

// Run setup
setup();
