<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Unified Social Media Platform - Demo</title>
    
    <!-- Material-UI CSS -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Roboto', sans-serif;
            background-color: #f5f5f5;
            color: #212121;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: linear-gradient(135deg, #1976d2, #dc004e);
            color: white;
            padding: 20px 0;
            text-align: center;
            margin-bottom: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            font-size: 2.5rem;
            font-weight: 600;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        
        .feature-card {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        
        .feature-icon {
            font-size: 3rem;
            margin-bottom: 15px;
            display: block;
        }
        
        .facebook { color: #1877f2; }
        .instagram { color: #e4405f; }
        .twitter { color: #1da1f2; }
        .whatsapp { color: #25d366; }
        .youtube { color: #ff0000; }
        
        .feature-card h3 {
            font-size: 1.5rem;
            margin-bottom: 15px;
            color: #333;
        }
        
        .feature-list {
            list-style: none;
        }
        
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
            display: flex;
            align-items: center;
        }
        
        .feature-list li:last-child {
            border-bottom: none;
        }
        
        .feature-list li::before {
            content: "✓";
            color: #4caf50;
            font-weight: bold;
            margin-right: 10px;
        }
        
        .demo-section {
            background: white;
            border-radius: 12px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .demo-section h2 {
            color: #1976d2;
            margin-bottom: 20px;
            font-size: 2rem;
        }
        
        .mockup {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .post-mockup {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        
        .user-info {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(45deg, #1976d2, #dc004e);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            margin-right: 12px;
        }
        
        .user-details h4 {
            margin: 0;
            color: #333;
        }
        
        .user-details p {
            margin: 0;
            color: #666;
            font-size: 0.9rem;
        }
        
        .post-content {
            margin-bottom: 15px;
            line-height: 1.6;
        }
        
        .post-actions {
            display: flex;
            gap: 20px;
            padding-top: 15px;
            border-top: 1px solid #eee;
        }
        
        .action-btn {
            display: flex;
            align-items: center;
            gap: 5px;
            color: #666;
            text-decoration: none;
            padding: 8px 12px;
            border-radius: 6px;
            transition: background-color 0.3s ease;
        }
        
        .action-btn:hover {
            background-color: #f0f0f0;
        }
        
        .setup-instructions {
            background: linear-gradient(135deg, #e3f2fd, #f3e5f5);
            border-radius: 12px;
            padding: 30px;
            margin-top: 30px;
        }
        
        .setup-instructions h2 {
            color: #1976d2;
            margin-bottom: 20px;
        }
        
        .code-block {
            background: #263238;
            color: #ffffff;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            overflow-x: auto;
        }
        
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .warning h3 {
            color: #856404;
            margin-bottom: 10px;
        }
        
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background: linear-gradient(135deg, #1976d2, #dc004e);
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 500;
            transition: transform 0.3s ease;
            margin: 10px 10px 10px 0;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .tech-stack {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .tech-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .features-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Unified Social Media Platform</h1>
            <p>Combining Facebook, Instagram, Twitter, WhatsApp & YouTube in One Platform</p>
        </div>

        <div class="warning">
            <h3>⚠️ Node.js Required</h3>
            <p>To run the full interactive demo, you need to install Node.js first. This HTML file shows you what the platform will look like and provides setup instructions.</p>
        </div>

        <div class="features-grid">
            <div class="feature-card">
                <span class="material-icons feature-icon facebook">people</span>
                <h3>Facebook Features</h3>
                <ul class="feature-list">
                    <li>News Feed & Timeline</li>
                    <li>Friend Connections</li>
                    <li>Groups & Pages</li>
                    <li>Events & Marketplace</li>
                    <li>Photo Albums</li>
                </ul>
            </div>

            <div class="feature-card">
                <span class="material-icons feature-icon instagram">photo_camera</span>
                <h3>Instagram Features</h3>
                <ul class="feature-list">
                    <li>Photo & Video Posts</li>
                    <li>Stories & Reels</li>
                    <li>Live Streaming</li>
                    <li>Hashtag Discovery</li>
                    <li>Visual Filters</li>
                </ul>
            </div>

            <div class="feature-card">
                <span class="material-icons feature-icon twitter">chat_bubble</span>
                <h3>Twitter Features</h3>
                <ul class="feature-list">
                    <li>Microblogging (Tweets)</li>
                    <li>Retweets & Quotes</li>
                    <li>Trending Topics</li>
                    <li>Real-time Timeline</li>
                    <li>Audio Spaces</li>
                </ul>
            </div>

            <div class="feature-card">
                <span class="material-icons feature-icon whatsapp">message</span>
                <h3>WhatsApp Features</h3>
                <ul class="feature-list">
                    <li>End-to-End Encryption</li>
                    <li>Group Chats</li>
                    <li>Voice & Video Calls</li>
                    <li>File Sharing</li>
                    <li>Status Updates</li>
                </ul>
            </div>

            <div class="feature-card">
                <span class="material-icons feature-icon youtube">play_circle</span>
                <h3>YouTube Features</h3>
                <ul class="feature-list">
                    <li>Video Upload & Streaming</li>
                    <li>Channel Management</li>
                    <li>Subscriptions</li>
                    <li>Live Broadcasting</li>
                    <li>Creator Analytics</li>
                </ul>
            </div>

            <div class="feature-card">
                <span class="material-icons feature-icon" style="color: #6200ea;">integration_instructions</span>
                <h3>Unified Experience</h3>
                <ul class="feature-list">
                    <li>Single Login System</li>
                    <li>Cross-Platform Sharing</li>
                    <li>Unified Notifications</li>
                    <li>Seamless Navigation</li>
                    <li>Professional Design</li>
                </ul>
            </div>
        </div>

        <div class="demo-section">
            <h2>🎮 Demo Preview</h2>
            <p>Here's what your unified social media platform will look like:</p>
            
            <div class="mockup">
                <h3>📱 Main Dashboard</h3>
                <div class="post-mockup">
                    <div class="user-info">
                        <div class="avatar">JD</div>
                        <div class="user-details">
                            <h4>John Doe ✓</h4>
                            <p>@johndoe • 2 hours ago</p>
                        </div>
                    </div>
                    <div class="post-content">
                        Just launched my new project! Excited to share it with everyone. 🚀
                    </div>
                    <div class="post-actions">
                        <a href="#" class="action-btn">
                            <span class="material-icons">thumb_up</span>
                            24 Likes
                        </a>
                        <a href="#" class="action-btn">
                            <span class="material-icons">comment</span>
                            8 Comments
                        </a>
                        <a href="#" class="action-btn">
                            <span class="material-icons">share</span>
                            3 Shares
                        </a>
                    </div>
                </div>

                <div class="post-mockup">
                    <div class="user-info">
                        <div class="avatar">JS</div>
                        <div class="user-details">
                            <h4>Jane Smith</h4>
                            <p>@janesmith • 4 hours ago</p>
                        </div>
                    </div>
                    <div class="post-content">
                        Beautiful sunset from my vacation in Bali! 🌅
                        <div style="background: linear-gradient(45deg, #ff6b6b, #feca57); height: 200px; border-radius: 8px; margin-top: 10px; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold;">
                            [Image: Sunset in Bali]
                        </div>
                    </div>
                    <div class="post-actions">
                        <a href="#" class="action-btn">
                            <span class="material-icons">thumb_up</span>
                            156 Likes
                        </a>
                        <a href="#" class="action-btn">
                            <span class="material-icons">comment</span>
                            23 Comments
                        </a>
                        <a href="#" class="action-btn">
                            <span class="material-icons">share</span>
                            12 Shares
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h2>🛠️ Technology Stack</h2>
            <div class="tech-stack">
                <div class="tech-item">
                    <strong>Frontend</strong><br>
                    React + TypeScript<br>
                    Material-UI
                </div>
                <div class="tech-item">
                    <strong>Backend</strong><br>
                    Node.js + Express<br>
                    Socket.io
                </div>
                <div class="tech-item">
                    <strong>Database</strong><br>
                    PostgreSQL<br>
                    Redis Cache
                </div>
                <div class="tech-item">
                    <strong>Real-time</strong><br>
                    WebSocket<br>
                    Live Updates
                </div>
            </div>
        </div>

        <div class="setup-instructions">
            <h2>🚀 Get Started - Setup Instructions</h2>
            
            <h3>Step 1: Install Node.js</h3>
            <p>Download and install Node.js from: <a href="https://nodejs.org" target="_blank" style="color: #1976d2;">https://nodejs.org</a></p>
            
            <h3>Step 2: Install Dependencies</h3>
            <div class="code-block">
# Install root dependencies
npm install

# Install server dependencies
cd server
npm install

# Install client dependencies  
cd ../client
npm install
cd ..
            </div>

            <h3>Step 3: Start the Development Servers</h3>
            <div class="code-block">
# Start both frontend and backend
npm run dev

# OR start them separately:
# Terminal 1 - Backend
cd server && npm run dev

# Terminal 2 - Frontend  
cd client && npm start
            </div>

            <h3>Step 4: Open Your Website</h3>
            <p>Once both servers are running:</p>
            <ul style="margin: 15px 0; padding-left: 20px;">
                <li><strong>Frontend:</strong> <a href="http://localhost:3000" target="_blank" style="color: #1976d2;">http://localhost:3000</a></li>
                <li><strong>Backend API:</strong> <a href="http://localhost:5000" target="_blank" style="color: #1976d2;">http://localhost:5000</a></li>
            </ul>

            <h3>🎮 Demo Login</h3>
            <p>Use any email and password to login to the demo!</p>
            
            <div style="margin-top: 30px;">
                <a href="https://nodejs.org" target="_blank" class="btn">
                    📥 Download Node.js
                </a>
                <a href="https://github.com" target="_blank" class="btn">
                    📚 View Documentation
                </a>
            </div>
        </div>

        <div style="text-align: center; margin-top: 40px; padding: 20px; color: #666;">
            <p>🎉 <strong>Your Unified Social Media Platform is Ready!</strong></p>
            <p>Professional • Scalable • Feature-Rich</p>
        </div>
    </div>

    <script>
        // Add some interactivity
        document.querySelectorAll('.action-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                btn.style.background = '#e3f2fd';
                setTimeout(() => {
                    btn.style.background = '';
                }, 200);
            });
        });

        // Animate feature cards on scroll
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        document.querySelectorAll('.feature-card').forEach(card => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';
            card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            observer.observe(card);
        });
    </script>
</body>
</html>
