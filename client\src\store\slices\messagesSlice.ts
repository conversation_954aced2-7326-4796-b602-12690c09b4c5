import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface Message {
  id: string;
  content: string;
  type: string;
  senderId: string;
  conversationId: string;
  createdAt: string;
  sender: {
    id: string;
    username: string;
    displayName: string;
    avatar?: string;
  };
}

interface Conversation {
  id: string;
  name?: string;
  isGroup: boolean;
  participants: Array<{
    user: {
      id: string;
      username: string;
      displayName: string;
      avatar?: string;
    };
  }>;
  messages: Message[];
}

interface MessagesState {
  conversations: Conversation[];
  activeConversation: string | null;
  messages: Message[];
  isLoading: boolean;
  error: string | null;
}

const initialState: MessagesState = {
  conversations: [],
  activeConversation: null,
  messages: [],
  isLoading: false,
  error: null,
};

const messagesSlice = createSlice({
  name: 'messages',
  initialState,
  reducers: {
    fetchConversationsStart: (state) => {
      state.isLoading = true;
      state.error = null;
    },
    fetchConversationsSuccess: (state, action: PayloadAction<Conversation[]>) => {
      state.isLoading = false;
      state.conversations = action.payload;
      state.error = null;
    },
    fetchConversationsFailure: (state, action: PayloadAction<string>) => {
      state.isLoading = false;
      state.error = action.payload;
    },
    setActiveConversation: (state, action: PayloadAction<string>) => {
      state.activeConversation = action.payload;
    },
    addMessage: (state, action: PayloadAction<Message>) => {
      state.messages.push(action.payload);
    },
    setMessages: (state, action: PayloadAction<Message[]>) => {
      state.messages = action.payload;
    },
    clearMessages: (state) => {
      state.messages = [];
      state.activeConversation = null;
    },
  },
});

export const {
  fetchConversationsStart,
  fetchConversationsSuccess,
  fetchConversationsFailure,
  setActiveConversation,
  addMessage,
  setMessages,
  clearMessages,
} = messagesSlice.actions;

export default messagesSlice.reducer;
