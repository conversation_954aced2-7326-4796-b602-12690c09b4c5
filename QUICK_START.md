# 🚀 Quick Start Guide - Unified Social Media Platform

## Prerequisites
- Node.js (v16 or higher)
- npm or yarn
- Git

## 🎯 Get Your Test Website Running in 5 Minutes!

### Step 1: Install Dependencies
```bash
# Install root dependencies
npm install

# Install server dependencies
cd server
npm install

# Install client dependencies
cd ../client
npm install

# Go back to root
cd ..
```

### Step 2: Set Up Environment Variables
```bash
# Copy environment files
cp server/.env.example server/.env
cp client/.env.example client/.env
```

### Step 3: Start the Development Servers
```bash
# Start both frontend and backend simultaneously
npm run dev
```

**OR start them separately:**

```bash
# Terminal 1 - Start Backend Server
cd server
npm run dev

# Terminal 2 - Start Frontend Client
cd client
npm start
```

### Step 4: Access Your Website
Once both servers are running, open your browser and go to:

**🌐 Frontend (React App): http://localhost:3000**
**🔧 Backend API: http://localhost:5000**

## 🎮 Demo Features Available

### ✅ Working Features:
1. **Professional UI/UX Design**
   - Modern Material-UI interface
   - Responsive design (mobile & desktop)
   - Dark/Light theme support

2. **Authentication System**
   - Login page with demo credentials
   - Registration form
   - Protected routes

3. **Main Dashboard**
   - Facebook-style news feed
   - Post creation interface
   - Trending topics sidebar
   - User suggestions

4. **Navigation**
   - Responsive sidebar navigation
   - Top navigation bar with search
   - Profile menu

### 🔄 Demo Login:
- **Email**: Use any email (e.g., <EMAIL>)
- **Password**: Use any password
- The demo will automatically log you in!

## 📱 Pages Available:
- **Home** (`/`) - Main news feed
- **Profile** (`/profile`) - User profile page
- **Messages** (`/messages`) - Chat interface
- **Explore** (`/explore`) - Content discovery
- **Videos** (`/videos`) - Video platform
- **Groups** (`/groups`) - Communities
- **Settings** (`/settings`) - User preferences

## 🛠️ Development Commands

```bash
# Install all dependencies
npm run install:all

# Start development servers
npm run dev

# Start only backend
npm run server:dev

# Start only frontend
npm run client:dev

# Build for production
npm run build

# Run tests
npm test

# Lint code
npm run lint
```

## 🐳 Docker Setup (Alternative)

If you prefer using Docker:

```bash
# Start with Docker Compose
docker-compose up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down
```

## 📂 Project Structure
```
unified-social-platform/
├── client/          # React frontend (Port 3000)
├── server/          # Node.js backend (Port 5000)
├── shared/          # Shared utilities
├── docs/            # Documentation
└── docker/          # Docker configuration
```

## 🎨 UI Features Showcase

### Modern Design Elements:
- **Material Design 3** components
- **Professional color scheme** (Blue primary, Pink secondary)
- **Responsive grid layout**
- **Smooth animations** and transitions
- **Accessibility compliant** (WCAG 2.1 AA)

### Social Media Features:
- **Facebook-style** news feed and posts
- **Instagram-style** visual content layout
- **Twitter-style** trending topics
- **WhatsApp-style** messaging interface
- **YouTube-style** video sections

## 🔧 Troubleshooting

### Common Issues:

1. **Port already in use:**
   ```bash
   # Kill processes on ports 3000 and 5000
   npx kill-port 3000 5000
   ```

2. **Dependencies issues:**
   ```bash
   # Clear node_modules and reinstall
   rm -rf node_modules client/node_modules server/node_modules
   npm run install:all
   ```

3. **Environment variables:**
   - Make sure `.env` files exist in both `server/` and `client/` directories
   - Copy from `.env.example` files if missing

## 🚀 Next Steps

Once you have the demo running:

1. **Explore the UI** - Navigate through all pages
2. **Test responsiveness** - Try different screen sizes
3. **Check the code** - Review the component structure
4. **Plan features** - Decide which features to implement first
5. **Set up database** - When ready for real data

## 📞 Need Help?

If you encounter any issues:
1. Check the console for error messages
2. Ensure all dependencies are installed
3. Verify ports 3000 and 5000 are available
4. Check that Node.js version is 16+

## 🎯 What You'll See

When you visit **http://localhost:3000**, you'll see:

1. **Login Page** - Professional authentication interface
2. **Main Dashboard** - Complete social media layout
3. **Responsive Design** - Works on all devices
4. **Modern UI** - Material Design components
5. **Navigation** - Sidebar and top navigation
6. **Demo Content** - Sample posts and interactions

**Your unified social media platform is ready to explore!** 🎉
