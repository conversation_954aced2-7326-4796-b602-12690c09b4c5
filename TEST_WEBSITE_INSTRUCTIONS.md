# 🌐 Test Your Unified Social Media Platform

## 🚀 Quick Setup (2 Minutes)

### Option 1: Automated Setup
```bash
# Run the automated setup script
npm run setup

# Start the website
npm run dev
```

### Option 2: Manual Setup
```bash
# Install dependencies
npm install
cd server && npm install
cd ../client && npm install
cd ..

# Copy environment files
cp server/.env.example server/.env
cp client/.env.example client/.env

# Start development servers
npm run dev
```

## 🌐 Your Test Links

Once the setup is complete and servers are running:

### 🎯 **Main Website (Frontend)**
**http://localhost:3000**

### 🔧 **API Backend**
**http://localhost:5000**

### 📊 **Health Check**
**http://localhost:5000/health**

## 🎮 Demo Experience

### 1. **Login Page** (First Visit)
- **URL**: http://localhost:3000/login
- **Demo Credentials**: Use ANY email and password
- **Example**: 
  - Email: `<EMAIL>`
  - Password: `password123`

### 2. **Main Dashboard** (After Login)
- **URL**: http://localhost:3000/
- **Features**:
  - Facebook-style news feed
  - Professional navigation
  - Trending topics
  - User suggestions
  - Mock social posts

### 3. **Available Pages**:
- **Home**: http://localhost:3000/ (Main feed)
- **Profile**: http://localhost:3000/profile
- **Messages**: http://localhost:3000/messages
- **Explore**: http://localhost:3000/explore
- **Videos**: http://localhost:3000/videos
- **Groups**: http://localhost:3000/groups
- **Settings**: http://localhost:3000/settings

## 📱 What You'll See

### ✨ **Professional Design Features**:
1. **Modern Material-UI Interface**
   - Clean, professional layout
   - Responsive design (mobile & desktop)
   - Smooth animations

2. **Social Media Elements**:
   - Facebook-style news feed
   - Instagram-inspired visual layout
   - Twitter-like trending topics
   - WhatsApp-style messaging interface
   - YouTube-style video sections

3. **Interactive Components**:
   - Navigation sidebar
   - Search functionality
   - User profile menu
   - Post interactions (like, comment, share)
   - Notification badges

### 🎨 **UI Highlights**:
- **Color Scheme**: Professional blue and pink theme
- **Typography**: Clean Roboto font family
- **Layout**: Responsive grid system
- **Icons**: Material Design icons
- **Cards**: Elevated post cards with shadows

## 🔧 Development Features

### **Hot Reload**:
- Frontend changes update instantly
- Backend API changes restart automatically

### **Console Logs**:
- Check browser console for frontend logs
- Check terminal for backend logs

### **Error Handling**:
- Professional error pages
- Graceful fallbacks

## 📊 Server Status

### **Frontend Server** (React):
- **Port**: 3000
- **Status**: Check terminal for "webpack compiled"
- **URL**: http://localhost:3000

### **Backend Server** (Node.js):
- **Port**: 5000
- **Status**: Check terminal for "Server running on port 5000"
- **Health**: http://localhost:5000/health

## 🎯 Testing Checklist

### ✅ **Basic Functionality**:
- [ ] Website loads at http://localhost:3000
- [ ] Login page appears
- [ ] Can login with any credentials
- [ ] Dashboard loads after login
- [ ] Navigation works between pages
- [ ] Responsive design on mobile

### ✅ **UI/UX Testing**:
- [ ] Professional appearance
- [ ] Smooth animations
- [ ] Readable typography
- [ ] Proper spacing and layout
- [ ] Interactive elements respond

### ✅ **Social Features Demo**:
- [ ] News feed displays mock posts
- [ ] Sidebar shows trending topics
- [ ] User suggestions appear
- [ ] Profile menu works
- [ ] Search bar is functional

## 🚨 Troubleshooting

### **Common Issues**:

1. **Port 3000 already in use**:
   ```bash
   npx kill-port 3000
   npm run client:dev
   ```

2. **Port 5000 already in use**:
   ```bash
   npx kill-port 5000
   npm run server:dev
   ```

3. **Dependencies not installed**:
   ```bash
   rm -rf node_modules client/node_modules server/node_modules
   npm run setup
   ```

4. **Environment files missing**:
   ```bash
   cp server/.env.example server/.env
   cp client/.env.example client/.env
   ```

### **Check Server Status**:
```bash
# Check if servers are running
curl http://localhost:3000  # Frontend
curl http://localhost:5000/health  # Backend
```

## 🎉 Success Indicators

### **You'll know it's working when**:
1. ✅ Terminal shows "webpack compiled successfully"
2. ✅ Terminal shows "Server running on port 5000"
3. ✅ Browser opens to http://localhost:3000
4. ✅ Login page loads with professional design
5. ✅ Can login and see the main dashboard
6. ✅ Navigation between pages works smoothly

## 📞 Next Steps

### **After Testing**:
1. **Explore the codebase** - Check out the component structure
2. **Review the architecture** - See how everything connects
3. **Plan development** - Decide which features to build first
4. **Set up database** - When ready for real data storage
5. **Deploy to cloud** - When ready for production

## 🌟 **Your Unified Social Media Platform is Ready!**

**Main Test URL: http://localhost:3000**

Enjoy exploring your professional social media platform that combines the best features of Facebook, Instagram, Twitter, WhatsApp, and YouTube! 🚀
