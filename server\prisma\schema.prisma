// Prisma schema for Unified Social Media Platform
generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User Management
model User {
  id                String   @id @default(cuid())
  email             String   @unique
  username          String   @unique
  firstName         String
  lastName          String
  displayName       String?
  bio               String?
  avatar            String?
  coverPhoto        String?
  dateOfBirth       DateTime?
  location          String?
  website           String?
  verified          Boolean  @default(false)
  isPrivate         <PERSON>ole<PERSON>  @default(false)
  isActive          Boolean  @default(true)
  lastSeen          DateTime @default(now())
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  // Authentication
  password          String
  emailVerified     <PERSON>olean  @default(false)
  emailVerifiedAt   DateTime?
  twoFactorEnabled  Boolean  @default(false)
  twoFactorSecret   String?

  // Social Features
  posts             Post[]
  comments          Comment[]
  likes             Like[]
  shares            Share[]
  stories           Story[]
  
  // Relationships
  following         Follow[] @relation("UserFollowing")
  followers         Follow[] @relation("UserFollowers")
  friendRequestsSent FriendRequest[] @relation("FriendRequestSender")
  friendRequestsReceived FriendRequest[] @relation("FriendRequestReceiver")
  
  // Messaging
  sentMessages      Message[] @relation("MessageSender")
  receivedMessages  Message[] @relation("MessageReceiver")
  conversations     ConversationParticipant[]
  
  // Groups & Communities
  groupMemberships  GroupMember[]
  groupsCreated     Group[] @relation("GroupCreator")
  
  // Media
  videos            Video[]
  channels          Channel[]
  subscriptions     Subscription[]
  
  // Notifications
  notifications     Notification[]
  
  @@map("users")
}

// Content Management
model Post {
  id          String   @id @default(cuid())
  content     String?
  type        PostType @default(TEXT)
  visibility  PostVisibility @default(PUBLIC)
  authorId    String
  author      User     @relation(fields: [authorId], references: [id], onDelete: Cascade)
  
  // Media attachments
  media       Media[]
  
  // Engagement
  likes       Like[]
  comments    Comment[]
  shares      Share[]
  
  // Metadata
  location    String?
  tags        String[]
  mentions    String[]
  hashtags    String[]
  
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  @@map("posts")
}

model Comment {
  id        String   @id @default(cuid())
  content   String
  authorId  String
  author    User     @relation(fields: [authorId], references: [id], onDelete: Cascade)
  postId    String
  post      Post     @relation(fields: [postId], references: [id], onDelete: Cascade)
  parentId  String?
  parent    Comment? @relation("CommentReplies", fields: [parentId], references: [id])
  replies   Comment[] @relation("CommentReplies")
  
  likes     Like[]
  
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  @@map("comments")
}

model Like {
  id        String   @id @default(cuid())
  userId    String
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  postId    String?
  post      Post?    @relation(fields: [postId], references: [id], onDelete: Cascade)
  commentId String?
  comment   Comment? @relation(fields: [commentId], references: [id], onDelete: Cascade)
  
  createdAt DateTime @default(now())
  
  @@unique([userId, postId])
  @@unique([userId, commentId])
  @@map("likes")
}

model Share {
  id        String   @id @default(cuid())
  userId    String
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  postId    String
  post      Post     @relation(fields: [postId], references: [id], onDelete: Cascade)
  content   String?  // Optional comment when sharing
  
  createdAt DateTime @default(now())
  
  @@map("shares")
}

// Stories (Instagram/Facebook style)
model Story {
  id        String   @id @default(cuid())
  content   String?
  mediaUrl  String?
  mediaType MediaType?
  authorId  String
  author    User     @relation(fields: [authorId], references: [id], onDelete: Cascade)
  
  views     StoryView[]
  
  expiresAt DateTime
  createdAt DateTime @default(now())
  
  @@map("stories")
}

model StoryView {
  id      String @id @default(cuid())
  userId  String
  storyId String
  story   Story  @relation(fields: [storyId], references: [id], onDelete: Cascade)
  
  viewedAt DateTime @default(now())
  
  @@unique([userId, storyId])
  @@map("story_views")
}

// Media Management
model Media {
  id        String    @id @default(cuid())
  filename  String
  originalName String
  mimeType  String
  size      Int
  url       String
  thumbnailUrl String?
  type      MediaType
  
  // Relationships
  postId    String?
  post      Post?     @relation(fields: [postId], references: [id], onDelete: Cascade)
  
  // Metadata
  width     Int?
  height    Int?
  duration  Int?      // For videos in seconds
  
  createdAt DateTime  @default(now())
  
  @@map("media")
}

// Video Platform (YouTube-style)
model Video {
  id          String   @id @default(cuid())
  title       String
  description String?
  url         String
  thumbnailUrl String?
  duration    Int      // in seconds
  views       Int      @default(0)
  
  authorId    String
  author      User     @relation(fields: [authorId], references: [id], onDelete: Cascade)
  channelId   String?
  channel     Channel? @relation(fields: [channelId], references: [id])
  
  // Engagement
  likes       VideoLike[]
  comments    VideoComment[]
  
  // Metadata
  tags        String[]
  category    String?
  isPublic    Boolean  @default(true)
  
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  @@map("videos")
}

model Channel {
  id          String   @id @default(cuid())
  name        String
  description String?
  avatar      String?
  banner      String?
  
  ownerId     String
  owner       User     @relation(fields: [ownerId], references: [id], onDelete: Cascade)
  
  videos      Video[]
  subscribers Subscription[]
  
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  @@map("channels")
}

model Subscription {
  id        String  @id @default(cuid())
  userId    String
  user      User    @relation(fields: [userId], references: [id], onDelete: Cascade)
  channelId String
  channel   Channel @relation(fields: [channelId], references: [id], onDelete: Cascade)
  
  createdAt DateTime @default(now())
  
  @@unique([userId, channelId])
  @@map("subscriptions")
}

// Enums
enum PostType {
  TEXT
  IMAGE
  VIDEO
  LINK
  POLL
  EVENT
}

enum PostVisibility {
  PUBLIC
  FRIENDS
  PRIVATE
}

enum MediaType {
  IMAGE
  VIDEO
  AUDIO
  DOCUMENT
}

enum MessageType {
  TEXT
  IMAGE
  VIDEO
  AUDIO
  FILE
  LOCATION
}

enum NotificationType {
  LIKE
  COMMENT
  SHARE
  FOLLOW
  FRIEND_REQUEST
  MESSAGE
  MENTION
  VIDEO_UPLOAD
}

enum GroupRole {
  ADMIN
  MODERATOR
  MEMBER
}

enum FriendRequestStatus {
  PENDING
  ACCEPTED
  DECLINED
}

model VideoLike {
  id      String @id @default(cuid())
  userId  String
  user    User   @relation(fields: [userId], references: [id], onDelete: Cascade)
  videoId String
  video   Video  @relation(fields: [videoId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now())

  @@unique([userId, videoId])
  @@map("video_likes")
}

model VideoComment {
  id        String   @id @default(cuid())
  content   String
  authorId  String
  author    User     @relation(fields: [authorId], references: [id], onDelete: Cascade)
  videoId   String
  video     Video    @relation(fields: [videoId], references: [id], onDelete: Cascade)
  parentId  String?
  parent    VideoComment? @relation("VideoCommentReplies", fields: [parentId], references: [id])
  replies   VideoComment[] @relation("VideoCommentReplies")

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("video_comments")
}

// Relationships
model Follow {
  id          String @id @default(cuid())
  followerId  String
  follower    User   @relation("UserFollowing", fields: [followerId], references: [id], onDelete: Cascade)
  followingId String
  following   User   @relation("UserFollowers", fields: [followingId], references: [id], onDelete: Cascade)

  createdAt   DateTime @default(now())

  @@unique([followerId, followingId])
  @@map("follows")
}

model FriendRequest {
  id         String              @id @default(cuid())
  senderId   String
  sender     User                @relation("FriendRequestSender", fields: [senderId], references: [id], onDelete: Cascade)
  receiverId String
  receiver   User                @relation("FriendRequestReceiver", fields: [receiverId], references: [id], onDelete: Cascade)
  status     FriendRequestStatus @default(PENDING)

  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  @@unique([senderId, receiverId])
  @@map("friend_requests")
}

// Messaging System
model Conversation {
  id           String                    @id @default(cuid())
  name         String?                   // For group chats
  isGroup      Boolean                   @default(false)
  avatar       String?                   // Group avatar

  participants ConversationParticipant[]
  messages     Message[]

  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  @@map("conversations")
}

model ConversationParticipant {
  id             String       @id @default(cuid())
  userId         String
  user           User         @relation(fields: [userId], references: [id], onDelete: Cascade)
  conversationId String
  conversation   Conversation @relation(fields: [conversationId], references: [id], onDelete: Cascade)

  isAdmin        Boolean      @default(false)
  joinedAt       DateTime     @default(now())
  lastReadAt     DateTime?

  @@unique([userId, conversationId])
  @@map("conversation_participants")
}

model Message {
  id             String      @id @default(cuid())
  content        String?
  type           MessageType @default(TEXT)

  senderId       String
  sender         User        @relation("MessageSender", fields: [senderId], references: [id], onDelete: Cascade)
  receiverId     String?     // For direct messages
  receiver       User?       @relation("MessageReceiver", fields: [receiverId], references: [id])
  conversationId String
  conversation   Conversation @relation(fields: [conversationId], references: [id], onDelete: Cascade)

  // Media attachments
  mediaUrl       String?
  mediaType      MediaType?

  // Message status
  isRead         Boolean     @default(false)
  isEdited       Boolean     @default(false)
  isDeleted      Boolean     @default(false)

  // Reply functionality
  replyToId      String?
  replyTo        Message?    @relation("MessageReplies", fields: [replyToId], references: [id])
  replies        Message[]   @relation("MessageReplies")

  createdAt      DateTime    @default(now())
  updatedAt      DateTime    @updatedAt

  @@map("messages")
}

// Groups & Communities
model Group {
  id          String        @id @default(cuid())
  name        String
  description String?
  avatar      String?
  banner      String?
  isPrivate   Boolean       @default(false)

  creatorId   String
  creator     User          @relation("GroupCreator", fields: [creatorId], references: [id], onDelete: Cascade)

  members     GroupMember[]
  posts       GroupPost[]

  createdAt   DateTime      @default(now())
  updatedAt   DateTime      @updatedAt

  @@map("groups")
}

model GroupMember {
  id      String    @id @default(cuid())
  userId  String
  user    User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  groupId String
  group   Group     @relation(fields: [groupId], references: [id], onDelete: Cascade)
  role    GroupRole @default(MEMBER)

  joinedAt DateTime @default(now())

  @@unique([userId, groupId])
  @@map("group_members")
}

model GroupPost {
  id        String   @id @default(cuid())
  content   String
  authorId  String
  groupId   String
  group     Group    @relation(fields: [groupId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("group_posts")
}

// Notifications
model Notification {
  id        String           @id @default(cuid())
  type      NotificationType
  title     String
  message   String
  isRead    Boolean          @default(false)

  userId    String
  user      User             @relation(fields: [userId], references: [id], onDelete: Cascade)

  // Related entity IDs for navigation
  entityId  String?          // ID of the related post, comment, user, etc.
  entityType String?         // Type of the related entity

  createdAt DateTime         @default(now())

  @@map("notifications")
}
