import { Server, Socket } from 'socket.io';
import jwt from 'jsonwebtoken';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

interface AuthenticatedSocket extends Socket {
  userId?: string;
  username?: string;
}

interface JwtPayload {
  userId: string;
  email: string;
  username: string;
}

// Store active users and their socket connections
const activeUsers = new Map<string, string>(); // userId -> socketId
const userSockets = new Map<string, string>(); // socketId -> userId

export const setupSocketHandlers = (io: Server) => {
  // Authentication middleware for socket connections
  io.use(async (socket: AuthenticatedSocket, next) => {
    try {
      const token = socket.handshake.auth.token;
      
      if (!token) {
        return next(new Error('Authentication token required'));
      }

      const jwtSecret = process.env.JWT_SECRET;
      if (!jwtSecret) {
        return next(new Error('Server configuration error'));
      }

      const decoded = jwt.verify(token, jwtSecret) as JwtPayload;
      
      // Verify user exists and is active
      const user = await prisma.user.findUnique({
        where: { 
          id: decoded.userId,
          isActive: true
        },
        select: {
          id: true,
          username: true,
          isActive: true
        }
      });

      if (!user) {
        return next(new Error('User not found or inactive'));
      }

      socket.userId = user.id;
      socket.username = user.username;
      next();
    } catch (error) {
      next(new Error('Invalid authentication token'));
    }
  });

  io.on('connection', (socket: AuthenticatedSocket) => {
    console.log(`User ${socket.username} connected with socket ${socket.id}`);

    // Store user connection
    if (socket.userId) {
      activeUsers.set(socket.userId, socket.id);
      userSockets.set(socket.id, socket.userId);

      // Update user's last seen and online status
      prisma.user.update({
        where: { id: socket.userId },
        data: { lastSeen: new Date() }
      }).catch(console.error);

      // Notify friends that user is online
      socket.broadcast.emit('user_online', {
        userId: socket.userId,
        username: socket.username
      });
    }

    // Join user to their personal room for notifications
    if (socket.userId) {
      socket.join(`user_${socket.userId}`);
    }

    // Handle joining conversation rooms
    socket.on('join_conversation', async (conversationId: string) => {
      try {
        if (!socket.userId) return;

        // Verify user is participant in conversation
        const participant = await prisma.conversationParticipant.findUnique({
          where: {
            userId_conversationId: {
              userId: socket.userId,
              conversationId
            }
          }
        });

        if (participant) {
          socket.join(`conversation_${conversationId}`);
          console.log(`User ${socket.username} joined conversation ${conversationId}`);
        }
      } catch (error) {
        console.error('Error joining conversation:', error);
      }
    });

    // Handle leaving conversation rooms
    socket.on('leave_conversation', (conversationId: string) => {
      socket.leave(`conversation_${conversationId}`);
      console.log(`User ${socket.username} left conversation ${conversationId}`);
    });

    // Handle sending messages
    socket.on('send_message', async (data: {
      conversationId: string;
      content: string;
      type?: string;
      replyToId?: string;
    }) => {
      try {
        if (!socket.userId) return;

        const { conversationId, content, type = 'TEXT', replyToId } = data;

        // Verify user is participant
        const participant = await prisma.conversationParticipant.findUnique({
          where: {
            userId_conversationId: {
              userId: socket.userId,
              conversationId
            }
          }
        });

        if (!participant) {
          socket.emit('error', { message: 'Not authorized to send messages in this conversation' });
          return;
        }

        // Create message in database
        const message = await prisma.message.create({
          data: {
            content,
            type,
            senderId: socket.userId,
            conversationId,
            replyToId
          },
          include: {
            sender: {
              select: {
                id: true,
                username: true,
                displayName: true,
                avatar: true
              }
            },
            replyTo: {
              include: {
                sender: {
                  select: {
                    id: true,
                    username: true,
                    displayName: true
                  }
                }
              }
            }
          }
        });

        // Update conversation timestamp
        await prisma.conversation.update({
          where: { id: conversationId },
          data: { updatedAt: new Date() }
        });

        // Emit message to all participants in the conversation
        io.to(`conversation_${conversationId}`).emit('new_message', message);

        // Send push notifications to offline users
        const conversationParticipants = await prisma.conversationParticipant.findMany({
          where: { conversationId },
          include: {
            user: {
              select: {
                id: true,
                username: true,
                displayName: true
              }
            }
          }
        });

        conversationParticipants.forEach(participant => {
          if (participant.userId !== socket.userId && !activeUsers.has(participant.userId)) {
            // Send notification to offline user
            io.to(`user_${participant.userId}`).emit('notification', {
              type: 'MESSAGE',
              title: `New message from ${socket.username}`,
              message: content,
              data: { conversationId, messageId: message.id }
            });
          }
        });

      } catch (error) {
        console.error('Error sending message:', error);
        socket.emit('error', { message: 'Failed to send message' });
      }
    });

    // Handle typing indicators
    socket.on('typing_start', (conversationId: string) => {
      socket.to(`conversation_${conversationId}`).emit('user_typing', {
        userId: socket.userId,
        username: socket.username,
        conversationId
      });
    });

    socket.on('typing_stop', (conversationId: string) => {
      socket.to(`conversation_${conversationId}`).emit('user_stopped_typing', {
        userId: socket.userId,
        username: socket.username,
        conversationId
      });
    });

    // Handle post likes in real-time
    socket.on('like_post', async (data: { postId: string; liked: boolean }) => {
      try {
        if (!socket.userId) return;

        const { postId, liked } = data;

        // Get post author to send notification
        const post = await prisma.post.findUnique({
          where: { id: postId },
          select: {
            authorId: true,
            author: {
              select: {
                username: true,
                displayName: true
              }
            }
          }
        });

        if (post && post.authorId !== socket.userId) {
          // Notify post author
          io.to(`user_${post.authorId}`).emit('notification', {
            type: 'LIKE',
            title: liked ? 'New like on your post' : 'Like removed from your post',
            message: `${socket.username} ${liked ? 'liked' : 'unliked'} your post`,
            data: { postId, userId: socket.userId }
          });
        }

        // Broadcast like update to all connected users
        socket.broadcast.emit('post_like_update', {
          postId,
          userId: socket.userId,
          liked
        });

      } catch (error) {
        console.error('Error handling post like:', error);
      }
    });

    // Handle new post notifications
    socket.on('new_post', async (postId: string) => {
      try {
        if (!socket.userId) return;

        // Get user's followers to notify them
        const followers = await prisma.follow.findMany({
          where: { followingId: socket.userId },
          include: {
            follower: {
              select: {
                id: true,
                username: true
              }
            }
          }
        });

        // Notify followers about new post
        followers.forEach(follow => {
          io.to(`user_${follow.followerId}`).emit('notification', {
            type: 'POST',
            title: 'New post from someone you follow',
            message: `${socket.username} shared a new post`,
            data: { postId, userId: socket.userId }
          });
        });

      } catch (error) {
        console.error('Error handling new post notification:', error);
      }
    });

    // Handle disconnect
    socket.on('disconnect', () => {
      console.log(`User ${socket.username} disconnected`);

      if (socket.userId) {
        // Remove from active users
        activeUsers.delete(socket.userId);
        userSockets.delete(socket.id);

        // Update last seen
        prisma.user.update({
          where: { id: socket.userId },
          data: { lastSeen: new Date() }
        }).catch(console.error);

        // Notify friends that user is offline
        socket.broadcast.emit('user_offline', {
          userId: socket.userId,
          username: socket.username
        });
      }
    });

    // Handle errors
    socket.on('error', (error) => {
      console.error('Socket error:', error);
    });
  });

  // Helper function to send notification to specific user
  const sendNotificationToUser = (userId: string, notification: any) => {
    io.to(`user_${userId}`).emit('notification', notification);
  };

  // Helper function to check if user is online
  const isUserOnline = (userId: string): boolean => {
    return activeUsers.has(userId);
  };

  // Export helper functions for use in other parts of the application
  return {
    sendNotificationToUser,
    isUserOnline,
    getActiveUsers: () => Array.from(activeUsers.keys()),
    getSocketForUser: (userId: string) => activeUsers.get(userId)
  };
};
