import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';
import path from 'path';

// Load environment variables
dotenv.config();

const app = express();
const PORT = process.env.PORT || 5000;

// Middleware
app.use(cors({
  origin: process.env.CLIENT_URL || "http://localhost:3000",
  credentials: true
}));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    message: 'Unified Social Media Platform API is running!'
  });
});

// Demo API Routes
app.get('/api/demo', (req, res) => {
  res.json({
    success: true,
    message: 'Demo API is working!',
    features: [
      'Facebook-style social networking',
      'Instagram-style visual content',
      'Twitter-style microblogging',
      'WhatsApp-style messaging',
      'YouTube-style video platform'
    ]
  });
});

// Mock authentication endpoint
app.post('/api/auth/login', (req, res) => {
  const { email, password } = req.body;

  // Mock successful login for demo
  res.json({
    success: true,
    message: 'Login successful',
    data: {
      user: {
        id: '1',
        email: email,
        username: email.split('@')[0],
        firstName: 'Demo',
        lastName: 'User',
        displayName: 'Demo User',
        verified: true
      },
      token: 'demo-jwt-token-' + Date.now()
    }
  });
});

// Serve static files from uploads directory
app.use('/uploads', express.static(path.join(__dirname, '../uploads')));

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    message: 'Route not found',
    availableRoutes: [
      'GET /health',
      'GET /api/demo',
      'POST /api/auth/login'
    ]
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Server running on port ${PORT}`);
  console.log(`📱 Environment: ${process.env.NODE_ENV || 'development'}`);
  console.log(`🌐 Client URL: ${process.env.CLIENT_URL || 'http://localhost:3000'}`);
  console.log(`🔗 Health check: http://localhost:${PORT}/health`);
  console.log(`🎮 Demo API: http://localhost:${PORT}/api/demo`);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully');
  process.exit(0);
});
