{"name": "unified-social-platform-server", "version": "1.0.0", "description": "Backend server for unified social media platform", "main": "dist/index.js", "scripts": {"start": "node dist/index.js", "dev": "nodemon src/index.ts", "build": "tsc", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "db:migrate": "npx prisma migrate dev", "db:seed": "npx prisma db seed", "db:studio": "npx prisma studio"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "dotenv": "^16.3.1"}, "devDependencies": {"@types/express": "^4.17.17", "@types/cors": "^2.8.13", "@types/node": "^20.5.0", "nodemon": "^3.0.1", "typescript": "^5.1.6", "ts-node": "^10.9.1"}, "prisma": {"seed": "ts-node prisma/seed.ts"}}