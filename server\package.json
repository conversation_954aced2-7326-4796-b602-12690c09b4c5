{"name": "unified-social-platform-server", "version": "1.0.0", "description": "Backend server for unified social media platform", "main": "dist/index.js", "scripts": {"start": "node dist/index.js", "dev": "nodemon src/index.ts", "build": "tsc", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "db:migrate": "npx prisma migrate dev", "db:seed": "npx prisma db seed", "db:studio": "npx prisma studio"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.0.0", "morgan": "^1.10.0", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "passport": "^0.6.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "multer": "^1.4.5-lts.1", "sharp": "^0.32.5", "socket.io": "^4.7.2", "redis": "^4.6.8", "pg": "^8.11.3", "prisma": "^5.2.0", "@prisma/client": "^5.2.0", "express-rate-limit": "^6.10.0", "express-validator": "^7.0.1", "nodemailer": "^6.9.4", "uuid": "^9.0.0", "compression": "^1.7.4", "express-fileupload": "^1.4.0"}, "devDependencies": {"@types/express": "^4.17.17", "@types/cors": "^2.8.13", "@types/morgan": "^1.9.4", "@types/bcryptjs": "^2.4.2", "@types/jsonwebtoken": "^9.0.2", "@types/passport": "^1.0.12", "@types/passport-jwt": "^3.0.9", "@types/passport-local": "^1.0.35", "@types/multer": "^1.4.7", "@types/node": "^20.5.0", "@types/uuid": "^9.0.2", "@types/compression": "^1.7.2", "@types/express-fileupload": "^1.4.1", "@typescript-eslint/eslint-plugin": "^6.4.0", "@typescript-eslint/parser": "^6.4.0", "eslint": "^8.47.0", "jest": "^29.6.2", "@types/jest": "^29.5.4", "ts-jest": "^29.1.1", "nodemon": "^3.0.1", "typescript": "^5.1.6", "ts-node": "^10.9.1"}, "prisma": {"seed": "ts-node prisma/seed.ts"}}