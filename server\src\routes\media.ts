import express from 'express';
import multer from 'multer';
import path from 'path';
import { PrismaClient } from '@prisma/client';
import { AuthRequest } from '../middleware/auth';
import { asyncHandler, ValidationError } from '../middleware/errorHandler';

const router = express.Router();
const prisma = new PrismaClient();

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, 'uploads/');
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const fileFilter = (req: any, file: Express.Multer.File, cb: multer.FileFilterCallback) => {
  // Check file type
  const allowedTypes = /jpeg|jpg|png|gif|webp|mp4|webm|mov|avi/;
  const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
  const mimetype = allowedTypes.test(file.mimetype);

  if (mimetype && extname) {
    return cb(null, true);
  } else {
    cb(new Error('Invalid file type. Only images and videos are allowed.'));
  }
};

const upload = multer({
  storage,
  limits: {
    fileSize: parseInt(process.env.MAX_FILE_SIZE || '10485760') // 10MB default
  },
  fileFilter
});

// Upload single file
router.post('/upload', upload.single('file'), asyncHandler(async (req: AuthRequest, res) => {
  if (!req.file) {
    throw new ValidationError('No file uploaded');
  }

  const { originalname, filename, mimetype, size } = req.file;
  
  // Determine media type
  let mediaType: 'IMAGE' | 'VIDEO' | 'AUDIO' | 'DOCUMENT';
  if (mimetype.startsWith('image/')) {
    mediaType = 'IMAGE';
  } else if (mimetype.startsWith('video/')) {
    mediaType = 'VIDEO';
  } else if (mimetype.startsWith('audio/')) {
    mediaType = 'AUDIO';
  } else {
    mediaType = 'DOCUMENT';
  }

  // Save media record to database
  const media = await prisma.media.create({
    data: {
      filename,
      originalName: originalname,
      mimeType: mimetype,
      size,
      url: `/uploads/${filename}`,
      type: mediaType
    }
  });

  res.status(201).json({
    success: true,
    message: 'File uploaded successfully',
    data: { media }
  });
}));

// Upload multiple files
router.post('/upload-multiple', upload.array('files', 10), asyncHandler(async (req: AuthRequest, res) => {
  const files = req.files as Express.Multer.File[];
  
  if (!files || files.length === 0) {
    throw new ValidationError('No files uploaded');
  }

  const mediaRecords = await Promise.all(
    files.map(async (file) => {
      const { originalname, filename, mimetype, size } = file;
      
      let mediaType: 'IMAGE' | 'VIDEO' | 'AUDIO' | 'DOCUMENT';
      if (mimetype.startsWith('image/')) {
        mediaType = 'IMAGE';
      } else if (mimetype.startsWith('video/')) {
        mediaType = 'VIDEO';
      } else if (mimetype.startsWith('audio/')) {
        mediaType = 'AUDIO';
      } else {
        mediaType = 'DOCUMENT';
      }

      return prisma.media.create({
        data: {
          filename,
          originalName: originalname,
          mimeType: mimetype,
          size,
          url: `/uploads/${filename}`,
          type: mediaType
        }
      });
    })
  );

  res.status(201).json({
    success: true,
    message: 'Files uploaded successfully',
    data: { media: mediaRecords }
  });
}));

// Get media by ID
router.get('/:mediaId', asyncHandler(async (req: AuthRequest, res) => {
  const { mediaId } = req.params;

  const media = await prisma.media.findUnique({
    where: { id: mediaId }
  });

  if (!media) {
    return res.status(404).json({
      success: false,
      message: 'Media not found'
    });
  }

  res.json({
    success: true,
    data: { media }
  });
}));

// Serve uploaded files
router.get('/files/:filename', (req, res) => {
  const { filename } = req.params;
  const filePath = path.join(__dirname, '../../uploads', filename);
  
  res.sendFile(filePath, (err) => {
    if (err) {
      res.status(404).json({
        success: false,
        message: 'File not found'
      });
    }
  });
});

// Delete media
router.delete('/:mediaId', asyncHandler(async (req: AuthRequest, res) => {
  const { mediaId } = req.params;
  const userId = req.user!.id;

  const media = await prisma.media.findUnique({
    where: { id: mediaId },
    include: {
      post: {
        select: {
          authorId: true
        }
      }
    }
  });

  if (!media) {
    return res.status(404).json({
      success: false,
      message: 'Media not found'
    });
  }

  // Check if user owns the media (through post ownership)
  if (media.post && media.post.authorId !== userId) {
    return res.status(403).json({
      success: false,
      message: 'You can only delete your own media'
    });
  }

  await prisma.media.delete({
    where: { id: mediaId }
  });

  res.json({
    success: true,
    message: 'Media deleted successfully'
  });
}));

export default router;
