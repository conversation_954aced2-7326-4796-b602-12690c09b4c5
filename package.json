{"name": "unified-social-platform", "version": "1.0.0", "description": "A comprehensive social media platform combining Facebook, Instagram, Twitter, WhatsApp, and YouTube features", "main": "server/index.js", "scripts": {"setup": "node setup.js", "dev": "concurrently \"npm run server:dev\" \"npm run client:dev\"", "server:dev": "cd server && npm run dev", "client:dev": "cd client && npm start", "build": "cd client && npm run build", "start": "cd server && npm start", "install:all": "npm install && cd server && npm install && cd ../client && npm install", "test": "cd server && npm test && cd ../client && npm test", "lint": "cd server && npm run lint && cd ../client && npm run lint"}, "keywords": ["social-media", "facebook", "instagram", "twitter", "whatsapp", "youtube", "unified-platform"], "author": "Your Name", "license": "MIT", "devDependencies": {"concurrently": "^7.6.0"}, "workspaces": ["server", "client"]}