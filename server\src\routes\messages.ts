import express from 'express';
import { PrismaClient } from '@prisma/client';
import { AuthRequest } from '../middleware/auth';
import { async<PERSON>and<PERSON>, NotFoundError, ForbiddenError } from '../middleware/errorHandler';

const router = express.Router();
const prisma = new PrismaClient();

// Get user conversations
router.get('/conversations', asyncHandler(async (req: AuthRequest, res) => {
  const userId = req.user!.id;

  const conversations = await prisma.conversation.findMany({
    where: {
      participants: {
        some: {
          userId
        }
      }
    },
    include: {
      participants: {
        include: {
          user: {
            select: {
              id: true,
              username: true,
              displayName: true,
              avatar: true
            }
          }
        }
      },
      messages: {
        take: 1,
        orderBy: {
          createdAt: 'desc'
        },
        include: {
          sender: {
            select: {
              id: true,
              username: true,
              displayName: true,
              avatar: true
            }
          }
        }
      },
      _count: {
        select: {
          messages: true
        }
      }
    },
    orderBy: {
      updatedAt: 'desc'
    }
  });

  res.json({
    success: true,
    data: { conversations }
  });
}));

// Get messages in a conversation
router.get('/conversations/:conversationId/messages', asyncHandler(async (req: AuthRequest, res) => {
  const userId = req.user!.id;
  const { conversationId } = req.params;
  const { page = 1, limit = 50 } = req.query;

  // Check if user is participant
  const participant = await prisma.conversationParticipant.findUnique({
    where: {
      userId_conversationId: {
        userId,
        conversationId
      }
    }
  });

  if (!participant) {
    throw new ForbiddenError('You are not a participant in this conversation');
  }

  const messages = await prisma.message.findMany({
    where: {
      conversationId,
      isDeleted: false
    },
    include: {
      sender: {
        select: {
          id: true,
          username: true,
          displayName: true,
          avatar: true
        }
      },
      replyTo: {
        include: {
          sender: {
            select: {
              id: true,
              username: true,
              displayName: true
            }
          }
        }
      }
    },
    orderBy: {
      createdAt: 'desc'
    },
    skip: (Number(page) - 1) * Number(limit),
    take: Number(limit)
  });

  // Mark messages as read
  await prisma.message.updateMany({
    where: {
      conversationId,
      senderId: { not: userId },
      isRead: false
    },
    data: {
      isRead: true
    }
  });

  res.json({
    success: true,
    data: { messages: messages.reverse() }
  });
}));

// Send a message
router.post('/conversations/:conversationId/messages', asyncHandler(async (req: AuthRequest, res) => {
  const userId = req.user!.id;
  const { conversationId } = req.params;
  const { content, type = 'TEXT', mediaUrl, replyToId } = req.body;

  // Check if user is participant
  const participant = await prisma.conversationParticipant.findUnique({
    where: {
      userId_conversationId: {
        userId,
        conversationId
      }
    }
  });

  if (!participant) {
    throw new ForbiddenError('You are not a participant in this conversation');
  }

  const message = await prisma.message.create({
    data: {
      content,
      type,
      mediaUrl,
      senderId: userId,
      conversationId,
      replyToId
    },
    include: {
      sender: {
        select: {
          id: true,
          username: true,
          displayName: true,
          avatar: true
        }
      },
      replyTo: {
        include: {
          sender: {
            select: {
              id: true,
              username: true,
              displayName: true
            }
          }
        }
      }
    }
  });

  // Update conversation timestamp
  await prisma.conversation.update({
    where: { id: conversationId },
    data: { updatedAt: new Date() }
  });

  res.status(201).json({
    success: true,
    message: 'Message sent successfully',
    data: { message }
  });
}));

// Create a new conversation
router.post('/conversations', asyncHandler(async (req: AuthRequest, res) => {
  const userId = req.user!.id;
  const { participantIds, name, isGroup = false } = req.body;

  // Include current user in participants
  const allParticipantIds = [userId, ...participantIds];

  // For direct messages, check if conversation already exists
  if (!isGroup && participantIds.length === 1) {
    const existingConversation = await prisma.conversation.findFirst({
      where: {
        isGroup: false,
        participants: {
          every: {
            userId: {
              in: allParticipantIds
            }
          }
        }
      },
      include: {
        participants: {
          include: {
            user: {
              select: {
                id: true,
                username: true,
                displayName: true,
                avatar: true
              }
            }
          }
        }
      }
    });

    if (existingConversation) {
      return res.json({
        success: true,
        message: 'Conversation already exists',
        data: { conversation: existingConversation }
      });
    }
  }

  const conversation = await prisma.conversation.create({
    data: {
      name,
      isGroup,
      participants: {
        create: allParticipantIds.map((participantId, index) => ({
          userId: participantId,
          isAdmin: index === 0 // First user (creator) is admin
        }))
      }
    },
    include: {
      participants: {
        include: {
          user: {
            select: {
              id: true,
              username: true,
              displayName: true,
              avatar: true
            }
          }
        }
      }
    }
  });

  res.status(201).json({
    success: true,
    message: 'Conversation created successfully',
    data: { conversation }
  });
}));

// Delete a message
router.delete('/messages/:messageId', asyncHandler(async (req: AuthRequest, res) => {
  const userId = req.user!.id;
  const { messageId } = req.params;

  const message = await prisma.message.findUnique({
    where: { id: messageId },
    select: {
      id: true,
      senderId: true
    }
  });

  if (!message) {
    throw new NotFoundError('Message not found');
  }

  if (message.senderId !== userId) {
    throw new ForbiddenError('You can only delete your own messages');
  }

  await prisma.message.update({
    where: { id: messageId },
    data: { isDeleted: true }
  });

  res.json({
    success: true,
    message: 'Message deleted successfully'
  });
}));

export default router;
