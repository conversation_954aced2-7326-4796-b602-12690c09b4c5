import React from 'react';
import {
  Container,
  Grid,
  Paper,
  Typography,
  Box,
  Card,
  CardContent,
  CardActions,
  Button,
  Avatar,
  Chip,
  Divider,
} from '@mui/material';
import {
  ThumbUp,
  Comment,
  Share,
  VideoLibrary,
  Photo,
  Event,
  TrendingUp,
} from '@mui/icons-material';

const Home: React.FC = () => {
  // Mock data for demonstration
  const mockPosts = [
    {
      id: 1,
      author: {
        name: '<PERSON>',
        username: '@johndoe',
        avatar: '',
        verified: true,
      },
      content: 'Just launched my new project! Excited to share it with everyone. 🚀',
      type: 'text',
      timestamp: '2 hours ago',
      likes: 24,
      comments: 8,
      shares: 3,
    },
    {
      id: 2,
      author: {
        name: '<PERSON>',
        username: '@jane<PERSON>',
        avatar: '',
        verified: false,
      },
      content: 'Beautiful sunset from my vacation in Bali! 🌅',
      type: 'image',
      timestamp: '4 hours ago',
      likes: 156,
      comments: 23,
      shares: 12,
    },
    {
      id: 3,
      author: {
        name: '<PERSON> News',
        username: '@technews',
        avatar: '',
        verified: true,
      },
      content: 'Breaking: New AI breakthrough announced at the conference today. This could change everything!',
      type: 'text',
      timestamp: '6 hours ago',
      likes: 89,
      comments: 34,
      shares: 45,
    },
  ];

  const trendingTopics = [
    '#TechNews',
    '#AI',
    '#WebDevelopment',
    '#SocialMedia',
    '#Innovation',
  ];

  const suggestedUsers = [
    { name: 'Alice Johnson', username: '@alice', followers: '1.2K' },
    { name: 'Bob Wilson', username: '@bobw', followers: '856' },
    { name: 'Carol Davis', username: '@carol', followers: '2.1K' },
  ];

  return (
    <Container maxWidth="lg">
      <Grid container spacing={3}>
        {/* Main Content */}
        <Grid item xs={12} md={8}>
          {/* Create Post */}
          <Paper sx={{ p: 3, mb: 3 }}>
            <Typography variant="h6" gutterBottom>
              What's on your mind?
            </Typography>
            <Box sx={{ display: 'flex', gap: 2, mt: 2 }}>
              <Button variant="outlined" startIcon={<Photo />}>
                Photo
              </Button>
              <Button variant="outlined" startIcon={<VideoLibrary />}>
                Video
              </Button>
              <Button variant="outlined" startIcon={<Event />}>
                Event
              </Button>
            </Box>
          </Paper>

          {/* Posts Feed */}
          {mockPosts.map((post) => (
            <Card key={post.id} sx={{ mb: 3 }}>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Avatar sx={{ mr: 2 }}>
                    {post.author.name[0]}
                  </Avatar>
                  <Box sx={{ flexGrow: 1 }}>
                    <Typography variant="subtitle1" fontWeight="bold">
                      {post.author.name}
                      {post.author.verified && (
                        <Chip
                          label="✓"
                          size="small"
                          color="primary"
                          sx={{ ml: 1, height: 20 }}
                        />
                      )}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {post.author.username} • {post.timestamp}
                    </Typography>
                  </Box>
                </Box>
                
                <Typography variant="body1" paragraph>
                  {post.content}
                </Typography>

                {post.type === 'image' && (
                  <Box
                    sx={{
                      height: 200,
                      backgroundColor: 'grey.200',
                      borderRadius: 1,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      mb: 2,
                    }}
                  >
                    <Typography color="text.secondary">
                      [Image Placeholder]
                    </Typography>
                  </Box>
                )}
              </CardContent>
              
              <Divider />
              
              <CardActions sx={{ justifyContent: 'space-between', px: 2 }}>
                <Button startIcon={<ThumbUp />} size="small">
                  {post.likes} Likes
                </Button>
                <Button startIcon={<Comment />} size="small">
                  {post.comments} Comments
                </Button>
                <Button startIcon={<Share />} size="small">
                  {post.shares} Shares
                </Button>
              </CardActions>
            </Card>
          ))}
        </Grid>

        {/* Sidebar */}
        <Grid item xs={12} md={4}>
          {/* Trending Topics */}
          <Paper sx={{ p: 2, mb: 3 }}>
            <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
              <TrendingUp sx={{ mr: 1 }} />
              Trending Topics
            </Typography>
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
              {trendingTopics.map((topic) => (
                <Chip
                  key={topic}
                  label={topic}
                  variant="outlined"
                  clickable
                  size="small"
                />
              ))}
            </Box>
          </Paper>

          {/* Suggested Users */}
          <Paper sx={{ p: 2, mb: 3 }}>
            <Typography variant="h6" gutterBottom>
              People You May Know
            </Typography>
            {suggestedUsers.map((user) => (
              <Box key={user.username} sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Avatar sx={{ mr: 2 }}>
                  {user.name[0]}
                </Avatar>
                <Box sx={{ flexGrow: 1 }}>
                  <Typography variant="subtitle2">
                    {user.name}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {user.followers} followers
                  </Typography>
                </Box>
                <Button size="small" variant="outlined">
                  Follow
                </Button>
              </Box>
            ))}
          </Paper>

          {/* Quick Stats */}
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              Your Activity
            </Typography>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
              <Typography variant="body2">Posts</Typography>
              <Typography variant="body2" fontWeight="bold">42</Typography>
            </Box>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
              <Typography variant="body2">Followers</Typography>
              <Typography variant="body2" fontWeight="bold">1,234</Typography>
            </Box>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
              <Typography variant="body2">Following</Typography>
              <Typography variant="body2" fontWeight="bold">567</Typography>
            </Box>
          </Paper>
        </Grid>
      </Grid>
    </Container>
  );
};

export default Home;
