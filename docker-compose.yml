version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: unified-social-postgres
    environment:
      POSTGRES_DB: unified_social_platform
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./docker/postgres/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - unified-social-network

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: unified-social-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - unified-social-network

  # Backend Server
  server:
    build:
      context: ./server
      dockerfile: Dockerfile
    container_name: unified-social-server
    environment:
      NODE_ENV: development
      PORT: 5000
      DATABASE_URL: ********************************************/unified_social_platform
      REDIS_URL: redis://redis:6379
      JWT_SECRET: your-super-secret-jwt-key-here
      CLIENT_URL: http://localhost:3000
    ports:
      - "5000:5000"
    volumes:
      - ./server:/app
      - /app/node_modules
      - ./uploads:/app/uploads
    depends_on:
      - postgres
      - redis
    networks:
      - unified-social-network
    command: npm run dev

  # Frontend Client
  client:
    build:
      context: ./client
      dockerfile: Dockerfile
    container_name: unified-social-client
    environment:
      REACT_APP_API_URL: http://localhost:5000/api
      REACT_APP_SOCKET_URL: http://localhost:5000
    ports:
      - "3000:3000"
    volumes:
      - ./client:/app
      - /app/node_modules
    depends_on:
      - server
    networks:
      - unified-social-network
    command: npm start

  # Nginx Reverse Proxy (for production)
  nginx:
    image: nginx:alpine
    container_name: unified-social-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./docker/nginx/ssl:/etc/nginx/ssl
    depends_on:
      - client
      - server
    networks:
      - unified-social-network
    profiles:
      - production

volumes:
  postgres_data:
  redis_data:

networks:
  unified-social-network:
    driver: bridge
