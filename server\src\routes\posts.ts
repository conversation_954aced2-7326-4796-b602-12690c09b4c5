import express from 'express';
import { PrismaClient } from '@prisma/client';
import { AuthRequest } from '../middleware/auth';
import { async<PERSON>and<PERSON>, NotFoundError, ForbiddenError } from '../middleware/errorHandler';

const router = express.Router();
const prisma = new PrismaClient();

// Create a new post
router.post('/', asyncHandler(async (req: AuthRequest, res) => {
  const userId = req.user!.id;
  const { content, type = 'TEXT', visibility = 'PUBLIC', location, hashtags = [] } = req.body;

  const post = await prisma.post.create({
    data: {
      content,
      type,
      visibility,
      location,
      hashtags,
      authorId: userId
    },
    include: {
      author: {
        select: {
          id: true,
          username: true,
          displayName: true,
          avatar: true,
          verified: true
        }
      },
      media: true,
      _count: {
        select: {
          likes: true,
          comments: true,
          shares: true
        }
      }
    }
  });

  res.status(201).json({
    success: true,
    message: 'Post created successfully',
    data: { post }
  });
}));

// Get news feed
router.get('/feed', asyncHandler(async (req: AuthRequest, res) => {
  const userId = req.user!.id;
  const { page = 1, limit = 20 } = req.query;

  // Get posts from followed users and own posts
  const posts = await prisma.post.findMany({
    where: {
      OR: [
        { authorId: userId },
        {
          author: {
            followers: {
              some: {
                followerId: userId
              }
            }
          }
        }
      ],
      visibility: {
        in: ['PUBLIC', 'FRIENDS']
      }
    },
    include: {
      author: {
        select: {
          id: true,
          username: true,
          displayName: true,
          avatar: true,
          verified: true
        }
      },
      media: true,
      _count: {
        select: {
          likes: true,
          comments: true,
          shares: true
        }
      }
    },
    orderBy: {
      createdAt: 'desc'
    },
    skip: (Number(page) - 1) * Number(limit),
    take: Number(limit)
  });

  res.json({
    success: true,
    data: { posts }
  });
}));

// Get a specific post
router.get('/:postId', asyncHandler(async (req: AuthRequest, res) => {
  const { postId } = req.params;

  const post = await prisma.post.findUnique({
    where: { id: postId },
    include: {
      author: {
        select: {
          id: true,
          username: true,
          displayName: true,
          avatar: true,
          verified: true
        }
      },
      media: true,
      comments: {
        include: {
          author: {
            select: {
              id: true,
              username: true,
              displayName: true,
              avatar: true,
              verified: true
            }
          },
          _count: {
            select: {
              likes: true,
              replies: true
            }
          }
        },
        orderBy: {
          createdAt: 'desc'
        }
      },
      _count: {
        select: {
          likes: true,
          comments: true,
          shares: true
        }
      }
    }
  });

  if (!post) {
    throw new NotFoundError('Post not found');
  }

  res.json({
    success: true,
    data: { post }
  });
}));

// Like/unlike a post
router.post('/:postId/like', asyncHandler(async (req: AuthRequest, res) => {
  const userId = req.user!.id;
  const { postId } = req.params;

  // Check if post exists
  const post = await prisma.post.findUnique({
    where: { id: postId }
  });

  if (!post) {
    throw new NotFoundError('Post not found');
  }

  // Check if already liked
  const existingLike = await prisma.like.findUnique({
    where: {
      userId_postId: {
        userId,
        postId
      }
    }
  });

  if (existingLike) {
    // Unlike
    await prisma.like.delete({
      where: {
        userId_postId: {
          userId,
          postId
        }
      }
    });

    res.json({
      success: true,
      message: 'Post unliked successfully',
      data: { liked: false }
    });
  } else {
    // Like
    await prisma.like.create({
      data: {
        userId,
        postId
      }
    });

    res.json({
      success: true,
      message: 'Post liked successfully',
      data: { liked: true }
    });
  }
}));

// Add comment to post
router.post('/:postId/comments', asyncHandler(async (req: AuthRequest, res) => {
  const userId = req.user!.id;
  const { postId } = req.params;
  const { content, parentId } = req.body;

  // Check if post exists
  const post = await prisma.post.findUnique({
    where: { id: postId }
  });

  if (!post) {
    throw new NotFoundError('Post not found');
  }

  const comment = await prisma.comment.create({
    data: {
      content,
      authorId: userId,
      postId,
      parentId
    },
    include: {
      author: {
        select: {
          id: true,
          username: true,
          displayName: true,
          avatar: true,
          verified: true
        }
      },
      _count: {
        select: {
          likes: true,
          replies: true
        }
      }
    }
  });

  res.status(201).json({
    success: true,
    message: 'Comment added successfully',
    data: { comment }
  });
}));

// Delete post
router.delete('/:postId', asyncHandler(async (req: AuthRequest, res) => {
  const userId = req.user!.id;
  const { postId } = req.params;

  const post = await prisma.post.findUnique({
    where: { id: postId },
    select: {
      id: true,
      authorId: true
    }
  });

  if (!post) {
    throw new NotFoundError('Post not found');
  }

  if (post.authorId !== userId) {
    throw new ForbiddenError('You can only delete your own posts');
  }

  await prisma.post.delete({
    where: { id: postId }
  });

  res.json({
    success: true,
    message: 'Post deleted successfully'
  });
}));

export default router;
