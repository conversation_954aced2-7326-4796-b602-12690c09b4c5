import express from 'express';
import { PrismaClient } from '@prisma/client';
import { AuthRequest } from '../middleware/auth';
import { asyncHandler, NotFoundError } from '../middleware/errorHandler';

const router = express.Router();
const prisma = new PrismaClient();

// Get user profile
router.get('/:userId', asyncHandler(async (req: AuthRequest, res) => {
  const { userId } = req.params;
  
  const user = await prisma.user.findUnique({
    where: { id: userId },
    select: {
      id: true,
      username: true,
      firstName: true,
      lastName: true,
      displayName: true,
      bio: true,
      avatar: true,
      coverPhoto: true,
      location: true,
      website: true,
      verified: true,
      isPrivate: true,
      createdAt: true,
      _count: {
        select: {
          posts: true,
          followers: true,
          following: true
        }
      }
    }
  });

  if (!user) {
    throw new NotFoundError('User not found');
  }

  res.json({
    success: true,
    data: { user }
  });
}));

// Update user profile
router.put('/profile', asyncHandler(async (req: AuthRequest, res) => {
  const userId = req.user!.id;
  const { firstName, lastName, bio, location, website, isPrivate } = req.body;

  const updatedUser = await prisma.user.update({
    where: { id: userId },
    data: {
      firstName,
      lastName,
      displayName: `${firstName} ${lastName}`,
      bio,
      location,
      website,
      isPrivate
    },
    select: {
      id: true,
      username: true,
      firstName: true,
      lastName: true,
      displayName: true,
      bio: true,
      avatar: true,
      coverPhoto: true,
      location: true,
      website: true,
      verified: true,
      isPrivate: true
    }
  });

  res.json({
    success: true,
    message: 'Profile updated successfully',
    data: { user: updatedUser }
  });
}));

// Search users
router.get('/search/:query', asyncHandler(async (req: AuthRequest, res) => {
  const { query } = req.params;
  const { page = 1, limit = 20 } = req.query;

  const users = await prisma.user.findMany({
    where: {
      OR: [
        { username: { contains: query, mode: 'insensitive' } },
        { displayName: { contains: query, mode: 'insensitive' } },
        { firstName: { contains: query, mode: 'insensitive' } },
        { lastName: { contains: query, mode: 'insensitive' } }
      ],
      isActive: true
    },
    select: {
      id: true,
      username: true,
      displayName: true,
      avatar: true,
      verified: true
    },
    skip: (Number(page) - 1) * Number(limit),
    take: Number(limit)
  });

  res.json({
    success: true,
    data: { users }
  });
}));

export default router;
