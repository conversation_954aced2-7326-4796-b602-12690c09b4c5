{"name": "unified-social-platform-client", "version": "1.0.0", "description": "Frontend client for unified social media platform", "private": true, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.15.0", "@reduxjs/toolkit": "^1.9.5", "react-redux": "^8.1.2", "@mui/material": "^5.14.5", "@mui/icons-material": "^5.14.3", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "socket.io-client": "^4.7.2", "axios": "^1.5.0", "react-query": "^3.39.3", "formik": "^2.4.3", "yup": "^1.2.0", "react-dropzone": "^14.2.3", "react-player": "^2.12.0", "react-webcam": "^7.1.1", "emoji-picker-react": "^4.4.9", "react-infinite-scroll-component": "^6.1.0", "react-virtualized": "^9.22.5", "date-fns": "^2.30.0", "lodash": "^4.17.21", "uuid": "^9.0.0"}, "devDependencies": {"@types/react": "^18.2.20", "@types/react-dom": "^18.2.7", "@types/lodash": "^4.14.196", "@types/uuid": "^9.0.2", "@typescript-eslint/eslint-plugin": "^6.4.0", "@typescript-eslint/parser": "^6.4.0", "eslint": "^8.47.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "typescript": "^5.1.6", "react-scripts": "5.0.1", "@testing-library/react": "^13.4.0", "@testing-library/jest-dom": "^5.17.0", "@testing-library/user-event": "^14.4.3"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint src/**/*.{ts,tsx}", "lint:fix": "eslint src/**/*.{ts,tsx} --fix"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:5000"}